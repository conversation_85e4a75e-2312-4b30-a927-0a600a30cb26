using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using Mapbox.Utils;
using System;
using System.Globalization;

/// <summary>
/// Classe abstraite servant de base pour tous les trackers GPS
/// </summary>
public abstract class AbstractGPSTracker : MonoBehaviour
{
    [Header("Map Settings")]
    [SerializeField] protected Vector2d defaultPosition = new Vector2d();

    [Header("Boats")]
    [SerializeField] public List<RegattaBoatData> boats = new List<RegattaBoatData>();

    [Header("Alert Settings")]
    [SerializeField] protected AudioClip alertSound;
    [SerializeField] protected float alertVolume = 0.8f;

    [Header("Start Line Settings")]
    [SerializeField] protected StartingLine startingLine;

    [Header("Start Sequence Timer")]
    [SerializeField] protected StartSequenceTimer _startSequenceTimerRef;

    public StartSequenceTimer StartSequenceTimerInstance => _startSequenceTimerRef;

    // --- Variables Internes ---
    protected Dictionary<string, RegattaBoatData> boatDictionary = new Dictionary<string, RegattaBoatData>();
    protected CultureInfo invariantCulture = CultureInfo.InvariantCulture;
    protected Mapbox.Unity.Map.AbstractMap map;
    protected AudioSource audioSource;
    protected Vector3 previousBoatPosition;

    public delegate void BoatAlertHandler(string boatId, Vector2d position, int alertType, string alertTypeName);
    public static event BoatAlertHandler OnBoatAlert;

    protected virtual void Start()
    {
        map = FindFirstObjectByType<Mapbox.Unity.Map.AbstractMap>();
        ValidateMapboxSettings();
        InitializeBoatDictionary();
        SetupAudioSource();

        // Event subscriptions are now handled by RaceController
    }

    protected void SetupAudioSource()
    {
        audioSource = gameObject.GetComponent<AudioSource>();
        if (audioSource == null && alertSound != null)
        {
            audioSource = gameObject.AddComponent<AudioSource>();
            audioSource.playOnAwake = false;
            audioSource.spatialBlend = 0; // 2D sound
            audioSource.volume = alertVolume;
        }
    }

    protected void InitializeBoatDictionary()
    {
        boatDictionary.Clear();
        foreach (var boat in boats)
        {
            if (boat != null && boat.boatObject != null && !string.IsNullOrEmpty(boat.associatedMacAddress))
            {
                if (!boatDictionary.ContainsKey(boat.associatedMacAddress))
                {
                    boatDictionary.Add(boat.associatedMacAddress, boat);
                    boat.isInitialized = false;
                    Debug.Log($"[{GetType().Name}] Bateau MAC {boat.associatedMacAddress} prêt pour recevoir une position GPS.");
                }
                else
                {
                    Debug.LogWarning($"[{GetType().Name}] Adresse MAC dupliquée: {boat.associatedMacAddress}");
                }
            }
            else if (boat != null)
            {
                string reason = "";
                if (boat.boatObject == null) reason += "GameObject manquant; ";
                if (string.IsNullOrEmpty(boat.associatedMacAddress)) reason += "MAC address vide; ";

                Debug.LogWarning($"[{GetType().Name}] Bateau invalide dans la liste initiale - {reason}(MAC: '{boat.associatedMacAddress}', GameObject: {boat.boatObject != null})");
            }
        }
        Debug.Log($"[{GetType().Name}] Dictionnaire initialisé avec {boatDictionary.Count} bateaux.");
    }

    /// <summary>
    /// Méthode abstraite à implémenter par les classes dérivées pour récupérer les données GPS
    /// </summary>
    protected abstract void StartDataFetching();

    /// <summary>
    /// Méthode commune pour mettre à jour la position d'un bateau
    /// </summary>
    protected void UpdateBoatPosition(TrackerData tracker)
    {
        string macAddress = tracker.mac;
        Debug.Log($"[{GetType().Name}] UpdateBoatPosition: Traitement des données pour MAC/ID: {macAddress}, Lat: {tracker.lat}, Lng: {tracker.lng}, AlertType: {tracker.alertType}");

        // Afficher tous les bateaux dans le dictionnaire pour le débogage
        Debug.Log($"[{GetType().Name}] UpdateBoatPosition: Bateaux disponibles dans le dictionnaire:");
        foreach (var entry in boatDictionary)
        {
            Debug.Log($"[{GetType().Name}] UpdateBoatPosition: - MAC/ID: '{entry.Key}', Bateau: {entry.Value?.boatId}");
        }

        RegattaBoatData boat = FindBoatByMacAddress(macAddress);

        if (boat == null)
        {
            Debug.LogWarning($"[{GetType().Name}] Aucun bateau trouvé pour MAC/ID: '{macAddress}'");
            return;
        }

        if (boat.boatObject == null)
        {
            Debug.LogError($"[{GetType().Name}] GameObject manquant pour le bateau MAC: {macAddress}");
            boatDictionary.Remove(macAddress);
            return;
        }

        var gpsSystem = boat.boatObject.GetComponent<GPSTracking>();
        if (gpsSystem == null)
        {
            Debug.LogWarning($"[{GetType().Name}] Composant GPSTracking non trouvé sur MAC {macAddress}.");
            return;
        }

        Vector2d newPosition = new Vector2d(tracker.lat, tracker.lng);
        Vector3 currentBoatPosition = boat.boatObject.transform.position;

        // Vérifier si le bateau est en avance
        // Vérifier si le bateau est en avance, sauf si une téléportation manuelle vient d'avoir lieu
        // Suppression de l'appel à HasBoatCrossedStartLine, remplacé par gestion via événements
        // if (startingLine != null && startingLine.enabled && !gpsSystem._ignoreGPSUpdatesTemporarily && startingLine.HasBoatCrossedStartLine(boat.boatObject.transform, previousBoatPosition))
        // {
        //     Debug.LogWarning($"ALERTE! Bateau MAC {macAddress} en avance à la position {newPosition}");
        //     OnBoatAlert?.Invoke(macAddress, newPosition, 3, "Départ anticipé");
        // }

        // Gestion via événements StartingLine.OnBoatCrossedStartLine et OnBoatCrossedFinishLine

        // --- LOGIQUE DE PLACEMENT INITIAL ---
        if (!boat.isInitialized)
        {
            // Toujours tenter de définir la position initiale si le bateau n'est pas encore initialisé
            // UnifiedBoatGPS gérera la mise en file d'attente si la carte n'est pas prête.
            gpsSystem.SetInitialPosition(newPosition);
            boat.isInitialized = true;
            boat.currentPosition = newPosition; // Mettre à jour la position actuelle du bateau
            Debug.Log($"[{GetType().Name}] UpdateBoatPosition: Bateau {macAddress} initialisé à {newPosition}.");
        }
        else
        {
            float heading = boat.currentPosition == newPosition ?
                boat.boatObject.transform.eulerAngles.y :
                CalculateHeading(boat.currentPosition, newPosition);

            boat.currentPosition = newPosition;

            // Mise à jour directe de la position réelle
            gpsSystem.UpdateRealPosition(newPosition, heading, tracker.lastUpdate);

            // Enregistrement des données (à implémenter par les classes dérivées si nécessaire)
            OnBoatPositionUpdated(macAddress, newPosition, heading, tracker.alertType);

            // Log de la vitesse si disponible
            if (tracker.speed > 0)
            {
                Debug.Log($"[{GetType().Name}] Vitesse du bateau MAC {macAddress}: {tracker.speed} nœuds");
            }
        }

        // Vérifier l'état d'alerte
        if (tracker.alert)
        {
            string alertTypeInfo = tracker.alertTypeName ?? GetAlertTypeName(tracker.alertType);
            Debug.LogWarning($"[{GetType().Name}] ALERTE! Bateau MAC {macAddress} en détresse (Type: {alertTypeInfo}, Code: {tracker.alertType}) à la position {newPosition}");
            PlayAlertSound();
            OnBoatAlert?.Invoke(macAddress, newPosition, tracker.alertType, alertTypeInfo);

            if (tracker.alertType == 2 && _startSequenceTimerRef != null && _startSequenceTimerRef.enabled)
            {
                Debug.LogWarning($"[{GetType().Name}] Double Touch détecté pour {macAddress}! Démarrage du timer.");
                _startSequenceTimerRef.StartSequence(8);
            }
        }

        previousBoatPosition = currentBoatPosition;
    }

    /// <summary>
    /// Méthode virtuelle appelée lorsqu'une position de bateau est mise à jour
    /// Peut être surchargée par les classes dérivées pour ajouter des fonctionnalités spécifiques
    /// </summary>
    protected virtual void OnBoatPositionUpdated(string macAddress, Vector2d position, float heading, int alertStatus)
    {
        // Implémentation par défaut vide - à surcharger si nécessaire
    }


    protected RegattaBoatData FindBoatByMacAddress(string macAddress)
    {
        if (string.IsNullOrEmpty(macAddress)) return null;

        if (boatDictionary.TryGetValue(macAddress, out RegattaBoatData boat))
        {
            if (boat != null && boat.boatObject != null)
            {
                return boat;
            }
            else if (boat != null)
            {
                boatDictionary.Remove(macAddress);
            }
        }
        return null;
    }

    protected string GetAlertTypeName(int alertType)
    {
        switch (alertType)
        {
            case 1: return "Touch Long";
            case 2: return "Double Touch";
            case 3: return "Départ Anticipé";
            case 4: return "Rappel Général";
            case 5: return "Rappel Individuel";
            case 6: return "Signal B1";
            case 7: return "Signal B2";
            case 8: return "Départ Course";
            case 9: return "Arrivée Course";
            default: return "Inconnu";
        }
    }

    protected void PlayAlertSound()
    {
        if (audioSource != null && alertSound != null && !audioSource.isPlaying)
        {
            audioSource.clip = alertSound;
            audioSource.Play();
        }
    }

    protected void ValidateMapboxSettings()
    {
        if (map == null)
        {
            Debug.LogError($"[{GetType().Name}] Référence Mapbox non définie !");
            return;
        }

        string initialCoordinates = string.Format(
            invariantCulture,
            "{0},{1}",
            defaultPosition.x.ToString("F6", invariantCulture),
            defaultPosition.y.ToString("F6", invariantCulture)
        );

        try
        {
            Vector2d testCoords = Mapbox.Unity.Utilities.Conversions.StringToLatLon(initialCoordinates);
            Debug.Log($"Coordonnées initiales valides: {initialCoordinates}");
        }
        catch (Exception e)
        {
            Debug.LogError($"Erreur de format des coordonnées: {e.Message}");
            defaultPosition = new Vector2d(48.8566, 2.3522);
        }
    }

    protected bool ValidateCoordinates(double lat, double lon)
    {
        // Vérifier si les coordonnées sont dans des limites raisonnables
        // Latitude: -90 à 90, Longitude: -180 à 180
        if (lat < -90 || lat > 90 || lon < -180 || lon > 180)
            return false;

        // Vérifier si les coordonnées sont proches de zéro (souvent signe d'erreur)
        if (Math.Abs(lat) < 0.001 && Math.Abs(lon) < 0.001)
            return false;

        return true;
    }

    protected float CalculateHeading(Vector2d oldPos, Vector2d newPos)
    {
        if (oldPos == newPos) return 0f;

        double dLon = (newPos.y - oldPos.y) * Mathf.Deg2Rad;
        double lat1 = oldPos.x * Mathf.Deg2Rad;
        double lat2 = newPos.x * Mathf.Deg2Rad;

        double y = Math.Sin(dLon) * Math.Cos(lat2);
        double x = Math.Cos(lat1) * Math.Sin(lat2) -
                  Math.Sin(lat1) * Math.Cos(lat2) * Math.Cos(dLon);

        if (Math.Abs(x) < 1e-9 && Math.Abs(y) < 1e-9) return 0f;

        double heading = Math.Atan2(y, x) * Mathf.Rad2Deg;
        return (float)((heading + 360) % 360);
    }

    public void AddBoat(string boatId, GameObject boatObject, Vector2d startPos)
    {
        if (boatObject == null)
        {
            Debug.LogError($"[{GetType().Name}] Tentative d'ajout d'un bateau avec un GameObject null: {boatId}");
            return;
        }

        RegattaBoatData newBoat = new RegattaBoatData(boatId, boatObject, startPos);

        // Validation de l'adresse MAC après création de newBoat
        if (string.IsNullOrEmpty(newBoat.associatedMacAddress))
        {
            Debug.LogError($"[{GetType().Name}] Adresse MAC non définie pour le bateau {boatId}");
            return;
        }

        if (!ValidateCoordinates(startPos.x, startPos.y))
        {
            Debug.LogWarning($"[{GetType().Name}] Position de départ invalide pour le bateau {boatId}, utilisation de la position par défaut");
            startPos = defaultPosition;
        }

        var gpsSystem = boatObject.GetComponent<GPSTracking>();
        if (gpsSystem == null)
        {
            Debug.LogError($"[{GetType().Name}] Le bateau {boatId} n'a pas de composant GPSTracking");
            return;
        }

        boats.Add(newBoat);
        boatDictionary[newBoat.associatedMacAddress] = newBoat;

        var floatingUI = boatObject.GetComponentInChildren<FloatingBoatUI>();
        if (floatingUI != null)
        {
            floatingUI.SetTarget(boatObject.transform);
            floatingUI.SetTeamName(boatId);
        }

        Debug.Log($"[{GetType().Name}] Nouveau bateau ajouté: MAC {newBoat.associatedMacAddress}");
    }

    public void HandleRecall(bool isGeneralRecall)
    {
        if (isGeneralRecall)
        {
            Debug.LogWarning($"[{GetType().Name}] Rappel général !");
            OnBoatAlert?.Invoke("all", new Vector2d(0, 0), 4, "Rappel général");
        }
        else
        {
            Debug.LogWarning($"[{GetType().Name}] Rappel individuel !");
            OnBoatAlert?.Invoke("individual", new Vector2d(0, 0), 5, "Rappel individuel");
        }
    }

    /// <summary>
    /// Déclenche l'événement OnBoatAlert
    /// </summary>
    protected void TriggerBoatAlert(string boatId, Vector2d position, int alertType, string alertTypeName)
    {
        OnBoatAlert?.Invoke(boatId, position, alertType, alertTypeName);
    }

    protected virtual void OnDestroy()
    {
        // À surcharger par les classes dérivées si nécessaire
    }
}
