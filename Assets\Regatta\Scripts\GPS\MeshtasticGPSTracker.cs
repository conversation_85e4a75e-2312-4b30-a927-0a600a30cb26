using UnityEngine;
using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Mapbox.Utils;

/// <summary>
/// Structure simple pour stocker les données d'une balise GPS active
/// </summary>
public struct ActiveTagData
{
    public string nodeId;
    public Vector2d lastPosition;
    public float lastTimestamp;
    public float lastHeading; // Ajout du cap

    public ActiveTagData(string id, Vector2d pos, float time, float heading)
    {
        nodeId = id;
        lastPosition = pos;
        lastTimestamp = time;
        lastHeading = heading;
    }
}

/// <summary>
/// Implémentation du tracker GPS pour les balises Meshtastic via UDP
/// </summary>
public class MeshtasticGPSTracker : AbstractGPSTracker
{
    [Header("Meshtastic Configuration")]
    [SerializeField] private MeshtasticUDPReceiver meshtasticReceiver;
    [SerializeField] private float startDelay = 2f;
    [SerializeField] private GameObject buoyPrefab; // Préfab pour les bouées
    [SerializeField] private string officialBoatNodeId; // ID du nœud Meshtastic pour le bateau officiel
    [SerializeField] private Transform officialBoatTransform; // Transform du bateau officiel

    [Header("Recording Settings")]
    [SerializeField] private string recordingsFolder = "Replays";
    [SerializeField] private float positionThreshold = 0f; // Distance minimale (mètres) pour enregistrement
    [SerializeField] private float timedRecordInterval = 1f; // Intervalle d'enregistrement temporisé

    private bool isTracking = false;
    private List<RaceSnapshot> currentRecording = new List<RaceSnapshot>();
    private string currentRecordingFile;
    private float lastRecordTime = 0f;

    private GameObject startBuoyObject; // Référence à la bouée de départ
    private GameObject endBuoyObject;   // Référence à la bouée d'arrivée

    public event Action<GameObject, GameObject> OnStartEndBuoysPlaced; // Événement pour notifier le RaceController

    // Dictionnaire pour stocker les balises GPS actives par NodeId
    private Dictionary<string, ActiveTagData> _activeTags = new Dictionary<string, ActiveTagData>();

    // Propriété publique pour accéder aux balises actives
    public Dictionary<string, ActiveTagData> ActiveTags
    {
        get { return _activeTags; }
    }

    protected override void Start()
    {
        base.Start();
        Debug.LogWarning($"[MeshtasticGPSTracker] Démarrage. officialBoatNodeId: {officialBoatNodeId}");

        // Utiliser le nouveau système GPS propre
        var cleanAdapter = gameObject.GetComponent(typeof(MonoBehaviour)) as MonoBehaviour;
        if (cleanAdapter != null && cleanAdapter.GetType().Name == "CleanMeshtasticAdapter")
        {
            Debug.LogWarning("[MeshtasticGPSTracker] ✅ Nouveau système GPS propre détecté");
        }

        // Vérifier si le bateau officiel est correctement configuré dans la liste 'boats'
        Invoke(nameof(StartTracking), startDelay);
    }

    /// <summary>
    /// Configure la liste des bateaux de course à suivre.
    /// Sera appelée par l'UI de sélection.
    /// </summary>
    public void ConfigureRaceBoats(Dictionary<string, RegattaBoatData> raceConfiguration)
    {
        Debug.LogWarning($"[MeshtasticGPSTracker] Configuration des bateaux de course reçue avec {raceConfiguration.Count} bateaux.");
        
        // Vider l'ancienne configuration
        boatDictionary.Clear();
        
        // Appliquer la nouvelle configuration
        boatDictionary = raceConfiguration;
        
        Debug.LogWarning($"[MeshtasticGPSTracker] Dictionnaire de bateaux mis à jour. {boatDictionary.Count} bateaux sont maintenant suivis.");
    }
    protected override void StartDataFetching()
    {
        Debug.LogWarning("Démarrage du tracking GPS Meshtastic...");

        if (boatDictionary == null)
        {
            boatDictionary = new Dictionary<string, RegattaBoatData>();
        }
        // boatDictionary.Clear();
        
        Debug.LogWarning("[MeshtasticGPSTracker] Le dictionnaire de bateaux est initialisé et attend la configuration depuis l'UI.");

        // Vérifier si le récepteur Meshtastic est assigné
        if (meshtasticReceiver == null)
        {
            meshtasticReceiver = FindFirstObjectByType<MeshtasticUDPReceiver>();
            if (meshtasticReceiver == null)
            {
                Debug.LogError("Aucun récepteur MeshtasticUDPReceiver trouvé dans la scène!");
                return;
            }
        }

        // S'abonner aux événements de réception de données
        meshtasticReceiver.OnGpsDataReceived += OnMeshtasticDataReceived;
        meshtasticReceiver.OnCannedMessageReceived += OnMeshtasticCannedMessageReceived;

        isTracking = true;
        Debug.LogWarning("Tracking GPS Meshtastic démarré avec succès.");
    }

    private void StartTracking()
    {
        StartDataFetching();
    }

    protected override void OnDestroy()
    {
        // Se désabonner des événements pour éviter les fuites de mémoire
        if (meshtasticReceiver != null)
        {
            meshtasticReceiver.OnGpsDataReceived -= OnMeshtasticDataReceived;
            meshtasticReceiver.OnCannedMessageReceived -= OnMeshtasticCannedMessageReceived;
        }

        isTracking = false;
        StopAllCoroutines();

        // Sauvegarder l'enregistrement en cours si nécessaire
        if (currentRecording.Count > 0)
        {
            SaveGPXFile();
        }

        // Appeler la méthode OnDestroy de la classe de base
        base.OnDestroy();
    }

    /// <summary>
    /// Méthode appelée lorsque de nouvelles données GPS sont reçues du MeshtasticUDPReceiver
    /// </summary>
    private void OnMeshtasticDataReceived(MeshtasticUDPReceiver.GpsData gpsData)
    {
        // Debug.Log($"[MeshtasticGPSTracker] OnMeshtasticDataReceived: Données GPS reçues pour NodeId: {gpsData.nodeId}, Lat: {gpsData.latitude}, Lon: {gpsData.longitude}"); // Désactivé: trop verbeux
        if (!isTracking || map == null)
        {
            Debug.LogWarning($"[MeshtasticGPSTracker] OnMeshtasticDataReceived: Non en tracking ou carte non initialisée. isTracking={isTracking}, map={(map != null ? "initialisée" : "null")}.");
            return;
        }

        ProcessGPSData(gpsData);
    }

    /// <summary>
    /// Méthode appelée lorsque de nouveaux messages canned sont reçus du MeshtasticUDPReceiver
    /// </summary>
    private void OnMeshtasticCannedMessageReceived(MeshtasticUDPReceiver.CannedMessage cannedMessage)
    {
        if (!isTracking || map == null) return;

        Debug.LogWarning($"[MeshtasticGPSTracker] Message canned reçu de {cannedMessage.nodeId}: {cannedMessage.message}");

        // Traiter le message canned
        ProcessCannedMessage(cannedMessage);
    }


    /// <summary>
    /// Normalise un NodeId (convertit les IDs numériques en format hexadécimal si nécessaire)
    /// </summary>
    private string NormalizeNodeId(string nodeId)
    {
        // Si c'est déjà au format !xxxxxxxx, on le retourne tel quel
        if (nodeId.StartsWith("!") && nodeId.Length == 9)
            return nodeId;

        // Si c'est un nombre décimal, on essaie de le convertir en hexadécimal
        if (uint.TryParse(nodeId, out uint numericId))
        {
            return $"!{numericId:x8}";
        }

        return nodeId; // Retourne tel quel si on ne peut pas le convertir
    }

    /// <summary>
    /// Méthode appelée par MeshtasticUDPReceiver lorsque de nouvelles données GPS sont disponibles
    /// </summary>
    public void ProcessGPSData(MeshtasticUDPReceiver.GpsData gpsData)
    {
        if (!isTracking || map == null) return;

        // Normaliser le NodeId reçu
        string normalizedNodeId = NormalizeNodeId(gpsData.nodeId);

        // FILTRE STRICT: Seulement vos IDs connus
        string[] authorizedNodeIds = { "!7d18cac4", "!da5394d0", "!7d18caec" };

        if (!authorizedNodeIds.Contains(normalizedNodeId))
        {
            Debug.LogWarning($"[MeshtasticGPSTracker] NodeId non autorisé rejeté: '{gpsData.nodeId}' (normalisé: '{normalizedNodeId}')");
            return;
        }

        Debug.Log($"[MeshtasticGPSTracker] NodeId autorisé traité: '{gpsData.nodeId}' (normalisé: '{normalizedNodeId}')");
        
        // Continuer le traitement normal...
        Vector2d gpsPosition = new Vector2d(gpsData.latitude, gpsData.longitude);

        // Mettre à jour ou ajouter la balise active
        // Calculer le cap si on a une position précédente
        float heading = 0f;
        if (_activeTags.ContainsKey(normalizedNodeId))
        {
            var existingTag = _activeTags[normalizedNodeId];
            heading = GPSPositionConverter.CalculateHeading(existingTag.lastPosition, gpsPosition);
            existingTag.lastPosition = gpsPosition;
            existingTag.lastTimestamp = (int)(gpsData.timestamp / 1000f);
            existingTag.lastHeading = heading;
            _activeTags[normalizedNodeId] = existingTag;
        }
        else
        {
            _activeTags.Add(normalizedNodeId, new ActiveTagData(normalizedNodeId, gpsPosition, gpsData.timestamp / 1000f, 0f));
            Debug.LogWarning($"[MeshtasticGPSTracker] Nouvelle balise active détectée: {normalizedNodeId}");
        }

        // Mettre à jour tous les bateaux associés à ce nodeId
        foreach (var boat in boats)
        {
            if (boat.associatedMacAddress == normalizedNodeId && boat.boatObject != null)
            {
                var gpsTracking = boat.boatObject.GetComponent<GPSTracking>();
                if (gpsTracking != null)
                {
                    // Transmettre la vitesse GPS si disponible
                    float gpsSpeed = gpsData.speed > 0 ? gpsData.speed : -1f;
                    gpsTracking.UpdateRealPosition(gpsPosition, heading, Time.time, gpsSpeed);

                    if (gpsSpeed > 0)
                    {
                        Debug.Log($"[MeshtasticGPSTracker] Vitesse GPS transmise pour {boat.boatId}: {gpsSpeed:F1}m/s");
                    }
                }
            }
        }


        // Traitement spécifique pour le bateau officiel
        if (!string.IsNullOrEmpty(officialBoatNodeId) && normalizedNodeId == officialBoatNodeId)
        {
            if (officialBoatTransform != null)
            {
                // Utiliser GPSTracking directement
                var officialBoatGPS = officialBoatTransform.GetComponent<GPSTracking>();
                if (officialBoatGPS != null)
                {
                    float gpsSpeed = gpsData.speed > 0 ? gpsData.speed : -1f;
                    officialBoatGPS.UpdateRealPosition(gpsPosition, heading, Time.time, gpsSpeed);
                    Debug.LogWarning($"[MeshtasticGPSTracker] Bateau officiel (NodeId: {gpsData.nodeId}) mis à jour via GPSTracking à GPS: {gpsData.latitude}, {gpsData.longitude}, vitesse: {gpsSpeed:F1}m/s");
                }
                else
                {
                    Debug.LogWarning($"[MeshtasticGPSTracker] officialBoatTransform ne contient pas de composant GPSTracking pour le bateau officiel (NodeId: {gpsData.nodeId})");
                }
            }
            else
            {
                Debug.LogWarning($"[MeshtasticGPSTracker] officialBoatTransform n'est pas assigné pour le bateau officiel (NodeId: {gpsData.nodeId})");
            }
            return;
        }

        // Traitement pour les bateaux de course via le système standard
        TrackerData trackerData = new TrackerData
        {
            mac = normalizedNodeId,
            lat = gpsData.latitude,
            lng = gpsData.longitude,
            speed = gpsData.speed, // Utiliser la vitesse GPS directement
            alertType = 0,
            lastUpdate = (int)(gpsData.timestamp / 1000f) // Conversion explicite en int
        };

        bool boatExists = boatDictionary.ContainsKey(trackerData.mac);

        if (boatExists)
        {
            // Déléguer la mise à jour au système de base qui gère l'initialisation et le mouvement
            UpdateBoatPosition(trackerData);
        }
        else
        {
            // Debug.LogWarning($"[MeshtasticGPSTracker] ⚠ Bateau inconnu '{trackerData.mac}' avec coordonnées valides. Vérification si c'est le bateau sécurité..."); // Désactivé: trop verbeux

            // CORRECTION: Vérifier si c'est le bateau sécurité (plusieurs NodeIds possibles)
            if (IsSecurityBoat(trackerData.mac))
            {
                Debug.LogWarning($"[MeshtasticGPSTracker] ✓ Le nœud '{trackerData.mac}' correspond au bateau sécurité. Traitement spécial appliqué.");
                HandleSecurityBoatGPS(trackerData);
                return; // Ne pas créer de bateau automatiquement pour le bateau sécurité
            }

            Debug.LogWarning($"[MeshtasticGPSTracker] ⚠ NodeId '{trackerData.mac}' non reconnu comme bateau sécurité. Création automatique désactivée pour éviter les conflits.");
            Debug.LogWarning($"[MeshtasticGPSTracker] ⚠ Si c'est un nouveau bateau de course, ajoutez-le manuellement à la configuration.");
            // TryCreateBoatForMeshtasticNode(trackerData.mac, new Vector2d(trackerData.lat, trackerData.lng)); // DÉSACTIVÉ
        }
    }

    /// <summary>
    /// Vérifie si un NodeId correspond au bateau sécurité
    /// </summary>
    private bool IsSecurityBoat(string nodeId)
    {
        // Liste des NodeIds connus pour le bateau sécurité
        string[] securityBoatNodeIds = {
            "!da5394d0",  // NodeId principal du bateau sécurité
            officialBoatNodeId  // NodeId configuré dans l'inspecteur
        };

        foreach (string securityNodeId in securityBoatNodeIds)
        {
            if (!string.IsNullOrEmpty(securityNodeId) && nodeId == securityNodeId)
            {
                return true;
            }
        }

        return false;
    }

    /// <summary>
    /// Gère les données GPS du bateau sécurité
    /// </summary>
    private void HandleSecurityBoatGPS(TrackerData trackerData)
    {
        Debug.LogWarning($"[MeshtasticGPSTracker] Traitement des données GPS du bateau sécurité: {trackerData.mac}");

        // Chercher le bateau sécurité dans la scène
        GameObject securityBoat = FindSecurityBoatInScene();

        if (securityBoat != null)
        {
            var securityBoatGPS = securityBoat.GetComponent<GPSTracking>();
            if (securityBoatGPS != null)
            {
                Vector2d gpsPosition = new Vector2d(trackerData.lat, trackerData.lng);
                securityBoatGPS.UpdateRealPosition(gpsPosition, 0f, Time.time);
                Debug.LogWarning($"[MeshtasticGPSTracker] Bateau sécurité mis à jour via GPSTracking à {gpsPosition}");
            }
            else
            {
                Debug.LogWarning($"[MeshtasticGPSTracker] Bateau sécurité sans composant GPSTracking. Impossible de mettre à jour la position.");
            }
        }
        else
        {
            Debug.LogWarning($"[MeshtasticGPSTracker] ⚠️ Bateau sécurité non trouvé dans la scène pour le NodeId {trackerData.mac}");
        }
    }

    /// <summary>
    /// Trouve le bateau sécurité dans la scène
    /// </summary>
    private GameObject FindSecurityBoatInScene()
    {
        // Méthode 1: Utiliser officialBoatTransform si configuré
        if (officialBoatTransform != null)
        {
            return officialBoatTransform.gameObject;
        }

        // Méthode 2: Chercher par nom
        GameObject securityBoat = GameObject.Find("Security");
        if (securityBoat != null)
        {
            return securityBoat;
        }

        // Méthode 3: Chercher un bateau avec GPSTracking qui a le bon NodeId
        var allBoatsWithGPS = FindObjectsByType<GPSTracking>(FindObjectsSortMode.None);
        foreach (var boatGPS in allBoatsWithGPS)
        {
            // Vérifier si ce bateau est configuré comme bateau sécurité (ou autre critère pertinent)
            if (boatGPS.gameObject.name.ToLower().Contains("security") ||
                boatGPS.gameObject.name.ToLower().Contains("secu"))
            {
                return boatGPS.gameObject;
            }
        }

        Debug.LogWarning("[MeshtasticGPSTracker] Aucun bateau sécurité trouvé dans la scène.");
        return null;
    }

    /// <summary>
    /// Vérifie si un bateau correspond à un nœud Meshtastic donné
    /// </summary>
    private bool DoesBoatMatchNode(GameObject boat, string nodeId)
    {
        // Méthode 1: Vérifier si le bateau a été configuré avec cette MAC address
        if (boatDictionary.ContainsKey(nodeId))
        {
            var boatData = boatDictionary[nodeId];
            return boatData != null && boatData.boatObject == boat;
        }

        // Méthode 2: Vérifier par nom ou autres critères
        // Vous pouvez ajouter d'autres méthodes de correspondance ici
        
        return false;
    }

    /// <summary>
    /// Méthode de debug pour vérifier la configuration des bateaux
    /// </summary>
    [ContextMenu("Debug - Show Boat Configuration")]
    public void DebugShowBoatConfiguration()
    {
        Debug.LogWarning("🔍 === DEBUG CONFIGURATION BATEAUX ===");

        Debug.LogWarning($"📊 Bateaux de course configurés: {boatDictionary.Count}");
        foreach (var kvp in boatDictionary)
        {
            var boat = kvp.Value;
            Debug.LogWarning($"  - NodeId: '{kvp.Key}' → Bateau: {boat.boatId} ({(boat.boatObject != null ? boat.boatObject.name : "null")})");
        }

        Debug.LogWarning($"🛡️ Bateau sécurité:");
        Debug.LogWarning($"  - officialBoatNodeId: '{officialBoatNodeId}'");
        Debug.LogWarning($"  - officialBoatTransform: {(officialBoatTransform != null ? officialBoatTransform.name : "null")}");
        Debug.LogWarning($"  - NodeId principal: '!da5394d0'");

        var securityBoat = FindSecurityBoatInScene();
        Debug.LogWarning($"  - Bateau sécurité trouvé: {(securityBoat != null ? securityBoat.name : "null")}");

        Debug.LogWarning("=== FIN DEBUG ===");
    }

    /// <summary>
    /// Traite les messages canned reçus
    /// </summary>
    private void ProcessCannedMessage(MeshtasticUDPReceiver.CannedMessage cannedMessage)
    {
        // Trouver le bateau associé au nodeId
        RegattaBoatData boat = FindBoatByMacAddress(cannedMessage.nodeId);

        if (boat == null)
        {
            Debug.LogWarning($"[MeshtasticGPSTracker] Message canned reçu pour un nodeId non configuré: {cannedMessage.nodeId}. Ignoré.");
            return;
        }

        // Traiter le message en fonction de son contenu
        switch (cannedMessage.message)
        {
            case "B1":
                Debug.LogWarning($"[MeshtasticGPSTracker] Message B1 reçu de {cannedMessage.nodeId} pour le bateau {boat.boatId}. Placement de la bouée de départ.");
                // Placer ou mettre à jour la bouée de départ
                startBuoyObject = PlaceBuoy(boat.currentPosition, "StartBuoy", startBuoyObject);
                TriggerBoatAlert(boat.associatedMacAddress, boat.currentPosition, 6, "Signal B1"); // Optionnel: conserver l'alerte visuelle
                CheckAndNotifyBuoysPlaced();
                break;
            case "B2":
                Debug.LogWarning($"[MeshtasticGPSTracker] Message B2 reçu de {cannedMessage.nodeId} pour le bateau {boat.boatId}. Placement de la bouée d'arrivée.");
                // Placer ou mettre à jour la bouée d'arrivée
                endBuoyObject = PlaceBuoy(boat.currentPosition, "EndBuoy", endBuoyObject);
                TriggerBoatAlert(boat.associatedMacAddress, boat.currentPosition, 7, "Signal B2"); // Optionnel: conserver l'alerte visuelle
                CheckAndNotifyBuoysPlaced();
                break;
            case "Start":
                Debug.LogWarning($"[MeshtasticGPSTracker] Message Start reçu de {cannedMessage.nodeId} pour le bateau {boat.boatId}. Déclenchement de la séquence de départ.");
                 if (StartSequenceTimerInstance != null)
                {
                    if (!StartSequenceTimerInstance.enabled)
                    {
                        StartSequenceTimerInstance.enabled = true; // Activer le script s'il est désactivé
                        Debug.LogWarning("[MeshtasticGPSTracker] StartSequenceTimer activé.");
                    }
                    Debug.LogWarning("[MeshtasticGPSTracker] Message Start détecté ! Démarrage du timer.");
                    StartSequenceTimerInstance.StartSequence(StartSequenceTimerInstance.totalTime / 60f);
                }
                TriggerBoatAlert(boat.associatedMacAddress, boat.currentPosition, 8, "Départ Course"); // Optionnel: conserver l'alerte visuelle
                break;
            case "FIN":
                Debug.LogWarning($"[MeshtasticGPSTracker] Signal FIN reçu pour le bateau {boat.boatId}");
                TriggerBoatAlert(boat.associatedMacAddress, boat.currentPosition, 9, "Arrivée Course");
                break;
            default:
                Debug.LogWarning($"[MeshtasticGPSTracker] Message canned inconnu reçu de {cannedMessage.nodeId}: {cannedMessage.message}.");
                break;
        }
    }


    protected override void OnBoatPositionUpdated(string macAddress, Vector2d position, float heading, int alertStatus)
    {
        // Enregistrer l'état du bateau pour l'export GPX
        RecordBoatState(macAddress, position, heading, alertStatus);
    }

    private void RecordBoatState(string macAddress, Vector2d position, float heading, int alertStatus)
    {
        // Filtrage des positions trop proches
        var lastPos = currentRecording.Count > 0 ?
            currentRecording[^1].boatStates.Find(b => b.macAddress == macAddress)?.position
            : null;

        if (lastPos != null && Vector2d.Distance(lastPos.Value, position) < positionThreshold)
            return;

        var newState = new BoatState
        {
            macAddress = macAddress,
            position = position,
            heading = heading,
            alertStatus = alertStatus,
            timestamp = Time.time
        };

        // Vérifier si on doit créer un nouveau snapshot ou ajouter à l'existant
        bool createNewSnapshot = false;

        // Si c'est le premier enregistrement ou si le temps écoulé depuis le dernier enregistrement est suffisant
        if (currentRecording.Count == 0 || (Time.time - lastRecordTime) >= timedRecordInterval)
        {
            createNewSnapshot = true;
            lastRecordTime = Time.time;
        }

        if (createNewSnapshot)
        {
            var snapshot = new RaceSnapshot
            {
                timestamp = Time.time,
                boatStates = new List<BoatState> { newState }
            };
            currentRecording.Add(snapshot);
        }
        else
        {
            // Ajouter au dernier snapshot
            var lastSnapshot = currentRecording[currentRecording.Count - 1];

            // Vérifier si ce bateau existe déjà dans le snapshot
            int existingIndex = lastSnapshot.boatStates.FindIndex(b => b.macAddress == macAddress);
            if (existingIndex >= 0)
            {
                // Remplacer l'état existant
                lastSnapshot.boatStates[existingIndex] = newState;
            }
            else
            {
                // Ajouter un nouvel état
                lastSnapshot.boatStates.Add(newState);
            }
        }
    }

    /// <summary>
    /// Démarre un nouvel enregistrement GPX
    /// </summary>
    public void StartRecording()
    {
        if (currentRecording.Count > 0)
        {
            Debug.LogWarning("Un enregistrement est déjà en cours. Arrêtez-le d'abord.");
            return;
        }

        currentRecording = new List<RaceSnapshot>();
        currentRecordingFile = $"race_{DateTime.Now:yyyyMMdd_HHmmss}";
        Debug.LogWarning($"Démarrage de l'enregistrement: {currentRecordingFile}");
    }

    /// <summary>
    /// Arrête l'enregistrement en cours et sauvegarde le fichier GPX
    /// </summary>
    public void StopRecording()
    {
        if (currentRecording.Count == 0)
        {
            Debug.LogWarning("Aucun enregistrement en cours.");
            return;
        }

        SaveGPXFile();
        currentRecording.Clear();
        Debug.LogWarning("Enregistrement arrêté et sauvegardé.");
    }

    /// <summary>
    /// Sauvegarde l'enregistrement actuel au format GPX
    /// </summary>
    private void SaveGPXFile()
    {
        if (currentRecording.Count == 0) return;

        try
        {
            // Créer le dossier s'il n'existe pas
            string folderPath = Path.Combine(Application.persistentDataPath, recordingsFolder);
            if (!Directory.Exists(folderPath))
            {
                Directory.CreateDirectory(folderPath);
            }

            string filePath = Path.Combine(folderPath, $"{currentRecordingFile}.gpx");

            // Générer le contenu GPX
            string gpxContent = GenerateGPXContent();

            // Écrire dans le fichier
            File.WriteAllText(filePath, gpxContent);

            Debug.LogWarning($"Fichier GPX sauvegardé: {filePath}");
        }
        catch (Exception e)
        {
            Debug.LogError($"Erreur lors de la sauvegarde du fichier GPX: {e.Message}");
        }
    }

    /// <summary>
    /// Essaie de créer automatiquement un bateau pour un identifiant Meshtastic
    /// </summary>
    private void TryCreateBoatForMeshtasticNode(string nodeId, Vector2d position)
    {
        Debug.LogWarning($"[MeshtasticGPSTracker] === CRÉATION AUTOMATIQUE DE BATEAU ===");
        Debug.LogWarning($"[MeshtasticGPSTracker] NodeId: '{nodeId}', Position: {position}");

        try
        {
            // Vérifier si un préfab de bateau est disponible
            GameObject boatPrefab = null;

            // Essayer de trouver un préfab de bateau dans les ressources
            boatPrefab = Resources.Load<GameObject>("Prefabs/Boat");
            Debug.LogWarning($"[MeshtasticGPSTracker] Préfab depuis Resources: {(boatPrefab != null ? "trouvé" : "non trouvé")}");

            // Si aucun préfab n'est trouvé, essayer de cloner un bateau existant
            if (boatPrefab == null && boats.Count > 0)
            {
                Debug.LogWarning($"[MeshtasticGPSTracker] Recherche d'un bateau existant à cloner parmi {boats.Count} bateaux...");
                foreach (var boat in boats)
                {
                    if (boat != null && boat.boatObject != null)
                    {
                        boatPrefab = boat.boatObject;
                        Debug.LogWarning($"[MeshtasticGPSTracker] ✓ Utilisation du bateau existant comme modèle: {boat.boatId}");
                        break;
                    }
                }
            }

            if (boatPrefab != null)
            {
                // Créer un nouveau GameObject pour le bateau
                GameObject newBoatObject = GameObject.Instantiate(boatPrefab);
                newBoatObject.name = $"Boat_{nodeId}";

                // Créer une nouvelle instance de RegattaBoatData
                RegattaBoatData newBoat = new RegattaBoatData($"Boat_{nodeId}", newBoatObject, position, nodeId); // Ajout de nodeId direct dans le constructeur

                // Ajouter le bateau à la liste et au dictionnaire
                boats.Add(newBoat);
                boatDictionary[nodeId] = newBoat;

                // Initialiser le bateau avec le nouveau système GPSTracking
                var gpsSystem = newBoatObject.GetComponent<GPSTracking>();
                if (gpsSystem != null)
                {
                    // La référence à la carte est gérée par le Awake/Start de GPSTracking.
                    // Forcer l'initialisation de la position initiale du bateau
                    // gpsSystem.QueueInitialPosition(position); // Méthode supprimée dans la refonte de GPSTracking
                    gpsSystem.SetInitialPosition(position); // Utiliser la nouvelle méthode directe
                    newBoat.isInitialized = true;
                    newBoat.currentPosition = position;

                    Debug.LogWarning($"[MeshtasticGPSTracker] Position initiale définie pour le bateau {nodeId} via GPSTracking.");
                }
                else
                {
                    Debug.LogWarning($"[MeshtasticGPSTracker] Le GameObject nouvellement créé pour {nodeId} ne contient pas le composant GPSTracking. Impossible d'initialiser correctement.");
                }

                Debug.LogWarning($"[MeshtasticGPSTracker] ✓ Bateau créé automatiquement pour {nodeId} à la position {position}");
                Debug.LogWarning($"[MeshtasticGPSTracker] ✓ Total bateaux après création: {boats.Count}");
                Debug.LogWarning($"[MeshtasticGPSTracker] ================================================");
            }
            else
            {
                Debug.LogError($"[MeshtasticGPSTracker] ✗ Impossible de créer un bateau pour {nodeId}: aucun préfab ou modèle disponible");
                Debug.LogWarning($"[MeshtasticGPSTracker] ================================================");
            }
        }
        catch (Exception e)
        {
                    Debug.LogError($"[MeshtasticGPSTracker] Erreur lors de la création du bateau pour {nodeId}: {e.Message}");
        }
    }

    /// <summary>
    /// Place une bouée sur la carte à une position donnée.
    /// </summary>
    /// <param name="position">La position GPS où placer la bouée.</param>
    /// <param name="buoyType">Le type de bouée (par exemple, "StartBuoy", "EndBuoy").</param>
    /// <param name="existingBuoy">Référence à une bouée existante à mettre à jour, ou null si nouvelle.</param>
    /// <returns>La référence de la bouée créée ou mise à jour.</returns>
    private GameObject PlaceBuoy(Vector2d position, string buoyType, GameObject existingBuoy)
    {
        if (buoyPrefab == null)
        {
            Debug.LogError("[MeshtasticGPSTracker] Impossible de placer la bouée: le préfab de bouée n'est pas assigné.");
            return existingBuoy; // Retourne l'existante si elle ne peut pas être mise à jour
        }

        Vector3 worldPosition = Vector3.zero;

        if (map != null)
        {
            worldPosition = map.GeoToWorldPosition(position);
        }
        else
        {
            Debug.LogWarning("[MeshtasticGPSTracker] Référence de carte non trouvée pour convertir la position de la bouée.");
            // Gérer le cas où la carte est nulle. Retourner l'existant ou null pour indiquer l'échec.
            return existingBuoy;
        }

        GameObject buoyObject;
        if (existingBuoy != null)
        {
            // Mettre à jour la position de la bouée existante
            buoyObject = existingBuoy;
            buoyObject.transform.position = worldPosition;
            Debug.LogWarning($"[MeshtasticGPSTracker] Bouée '{buoyType}' mise à jour à la position monde {worldPosition} (GPS: {position.x}, {position.y})");
        }
        else
        {
            // Instancier une nouvelle bouée
            buoyObject = Instantiate(buoyPrefab, worldPosition, Quaternion.identity);
            buoyObject.name = $"{buoyType}"; // Nom fixe pour faciliter la référence
            Debug.LogWarning($"[MeshtasticGPSTracker] Nouvelle bouée '{buoyType}' placée à la position monde {worldPosition} (GPS: {position.x}, {position.y})");
        }

        return buoyObject;
    }

    /// <summary>
    /// Vérifie si les deux bouées (départ et arrivée) sont placées et notifie le RaceController.
    /// </summary>
    private void CheckAndNotifyBuoysPlaced()
    {
        if (startBuoyObject != null && endBuoyObject != null)
        {
            Debug.LogWarning("[MeshtasticGPSTracker] Les bouées de départ et d'arrivée sont toutes deux placées. Notification du RaceController.");
            OnStartEndBuoysPlaced?.Invoke(startBuoyObject, endBuoyObject);
        }
    }


    private string GenerateGPXContent()
    {
        System.Text.StringBuilder sb = new System.Text.StringBuilder();

        // En-tête GPX
        sb.AppendLine("<?xml version=\"1.0\" encoding=\"UTF-8\"?>");
        sb.AppendLine("<gpx version=\"1.1\" creator=\"MeshtasticGPSTracker\" xmlns=\"http://www.topografix.com/GPX/1/1\">");

        // Regrouper les données par bateau
        Dictionary<string, List<BoatState>> boatTracks = new Dictionary<string, List<BoatState>>();

        foreach (var snapshot in currentRecording)
        {
            foreach (var state in snapshot.boatStates)
            {
                if (!boatTracks.ContainsKey(state.macAddress))
                {
                    boatTracks[state.macAddress] = new List<BoatState>();
                }
                boatTracks[state.macAddress].Add(state);
            }
        }

        // Créer une piste pour chaque bateau
        foreach (var kvp in boatTracks)
        {
            string macAddress = kvp.Key;
            List<BoatState> states = kvp.Value;

            // Trier par timestamp
            states.Sort((a, b) => a.timestamp.CompareTo(b.timestamp));

            // Créer la piste
            sb.AppendLine($"  <trk>");
            sb.AppendLine($"    <name>Boat {macAddress}</name>");
            sb.AppendLine($"    <trkseg>");

            // Ajouter chaque point
            foreach (var state in states)
            {
                // Convertir le timestamp en format ISO 8601
                DateTime dateTime = DateTime.Now.AddSeconds(-Time.time + state.timestamp);
                string timeStr = dateTime.ToString("yyyy-MM-ddTHH:mm:ssZ");

                sb.AppendLine($"      <trkpt lat=\"{state.position.x}\" lon=\"{state.position.y}\">");
                sb.AppendLine($"        <time>{timeStr}</time>");
                sb.AppendLine($"        <course>{state.heading}</course>");
                sb.AppendLine($"      </trkpt>");
            }
        }

        sb.AppendLine($"    </trkseg>");
        sb.AppendLine($"  </trk>");
        sb.AppendLine("</gpx>");

        return sb.ToString();
    }
}

