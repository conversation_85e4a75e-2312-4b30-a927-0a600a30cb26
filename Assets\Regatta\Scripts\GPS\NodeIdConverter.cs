using UnityEngine;

/// <summary>
/// Utilitaire pour convertir et tester les NodeIds Meshtastic
/// </summary>
public static class NodeIdConverter
{
    /// <summary>
    /// Convertit un ID numérique en format hexadécimal Meshtastic
    /// </summary>
    public static string ConvertNumericToHex(string numericId)
    {
        if (uint.TryParse(numericId, out uint id))
        {
            return $"!{id:x8}";
        }
        return numericId;
    }

    /// <summary>
    /// Convertit un ID hexadécimal en format numérique
    /// </summary>
    public static string ConvertHexToNumeric(string hexId)
    {
        if (hexId.StartsWith("!") && hexId.Length == 9)
        {
            string hex = hexId.Substring(1);
            if (uint.TryParse(hex, System.Globalization.NumberStyles.HexNumber, null, out uint id))
            {
                return id.ToString();
            }
        }
        return hexId;
    }

    /// <summary>
    /// Teste les conversions avec les IDs des logs
    /// </summary>
    [RuntimeInitializeOnLoadMethod]
    public static void TestConversions()
    {
        string[] testIds = { "3662910672", "2098776772", "2098776812" };
        
        Debug.Log("[NodeIdConverter] Test des conversions d'IDs:");
        foreach (string id in testIds)
        {
            string converted = ConvertNumericToHex(id);
            Debug.Log($"[NodeIdConverter] {id} -> {converted}");
        }
    }
}
