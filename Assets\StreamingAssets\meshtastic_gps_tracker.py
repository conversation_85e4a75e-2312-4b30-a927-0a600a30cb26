#!/usr/bin/env python3
# -*- coding: utf-8 -*-
#
# Meshtastic GUI Controller - Simplified UDP Relay
# Ce code se concentre sur la réception des messages Meshtastic (GPS, texte)
# et leur relais via UDP, sans fonctionnalités de configuration d'appareil.

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import queue
import threading
import time
import socket
import json
import logging

# Définir pub comme un objet vide au cas où l'import échoue
class DummyPub:
    AUTO_TOPIC = None
    def subscribe(self, *args, **kwargs):
        pass
pub = DummyPub()

# Ces imports peuvent nécessiter l'installation des packages
try:
    import meshtastic
    import meshtastic.serial_interface
    # Imports spécifiques Meshtastic. Protobufs de config sont supprimés.
    try:
        from pubsub import pub as real_pub
        pub = real_pub
    except ImportError:
        logging.warning("Module pubsub non trouvé, certaines fonctionnalités seront limitées")

    try:
        import sv_ttk
    except ImportError:
        logging.warning("Module sv_ttk non trouvé, le thème par défaut sera utilisé")

    try:
        import serial
    except ImportError:
        logging.warning("Module serial non trouvé, certaines fonctionnalités seront limitées")

except ImportError as e:
    logging.error(f"Erreur d'importation critique: {e}")
    logging.error("Veuillez installer les packages manquants avec:")
    logging.error("pip install meshtastic pypubsub sv-ttk pyserial")

# --- Configuration UDP ---
UNITY_IP = "127.0.0.1"
UNITY_PORT = 18830
LOG_LEVEL = logging.WARNING # Seuls les messages WARNING et ERROR seront affichés
UDP_CHECK_INTERVAL = 5000  # Vérifier l'état du socket UDP toutes les 5 secondes

# --- Variables Globales ---
meshtastic_interface = None
meshtastic_thread = None
stop_thread_event = threading.Event()
message_queue = queue.Queue()
udp_socket = None
is_meshtastic_running = False
# ------------------------

# --- Fonctions Meshtastic (tournent dans un thread séparé) ---

def on_receive(packet, interface):
    """Callback Meshtastic pour les paquets reçus."""
    global message_queue
    try:
        # Traitement spécial pour les paquets GPS
        if 'decoded' in packet and 'position' in packet['decoded']:
            position_data = packet['decoded']['position']
            
            # CORRECTION: Extraire le NodeId au bon format
            raw_from = packet.get('from', 'unknown')
            
            # Si c'est un nombre, le convertir en format !xxxxxxxx
            if isinstance(raw_from, int):
                node_id = f"!{raw_from:08x}"
            elif isinstance(raw_from, str) and not raw_from.startswith('!'):
                # Si c'est une string sans !, l'ajouter
                if raw_from.isdigit():
                    # Si c'est un nombre en string, convertir
                    node_id = f"!{int(raw_from):08x}"
                else:
                    node_id = f"!{raw_from}"
            else:
                node_id = str(raw_from)
            
            # VÉRIFICATION: Ne traiter que les IDs connus
            known_nodes = ["!7d18cac4", "!da5394d0"]  # VOS IDs CONNUS
            if node_id not in known_nodes:
                logging.warning(f"NodeId non autorisé ignoré: {node_id} (raw: {raw_from})")
                return
            
            # Extraire les données GPS avec vitesse
            gps_packet = {
                'type': 'gps',
                'nodeId': node_id,  # Utiliser l'ID formaté
                'latitude': position_data.get('latitude', 0.0),
                'longitude': position_data.get('longitude', 0.0),
                'speed': position_data.get('groundSpeed', 0.0),
                'heading': position_data.get('groundTrack', 0.0),
                'altitude': position_data.get('altitude', 0),
                'timestamp': int(time.time() * 1000)
            }
            
            # Envoyer directement via UDP
            send_to_unity(gps_packet)
            
            # Log pour debug
            speed_knots = gps_packet['speed'] * 1.944
            logging.info(f"GPS envoyé - Node: {gps_packet['nodeId']} (raw: {raw_from}), "
                        f"Vitesse: {gps_packet['speed']:.1f}m/s ({speed_knots:.1f} nœuds)")
        
        # Mettre le paquet brut dans la queue pour traitement par le GUI
        message_queue.put({'type': 'meshtastic_packet', 'data': packet})
    except Exception as e:
        logging.error(f"Erreur dans on_receive: {e}")

def on_connection(interface, topic=None):
    """Callback Meshtastic pour l'état de la connexion."""
    global message_queue, meshtastic_interface
    try:
        meshtastic_interface = interface
        logging.info(f"Interface connectée: {interface.__class__.__name__}")

        node_info = "Infos non disponibles"
        if hasattr(interface, 'myInfo') and interface.myInfo:
            myinfo = interface.myInfo
            node_num = getattr(myinfo, "my_node_num", "N/A")
            if hasattr(myinfo, "node_id"):
                node_id = myinfo.node_id
            elif hasattr(myinfo, "my_node_id"):
                node_id = myinfo.my_node_id
            else:
                node_id = "Non disponible"
            node_info = f"NodeNum={node_num}, ID={node_id}"
            logging.info(f"Infos noeud: {node_id}")
        else:
            logging.warning("myInfo non disponible sur l'interface")

        if hasattr(interface, 'localNode'):
            logging.debug("Interface prête")
        else:
            logging.warning("localNode non disponible sur l'interface")

        message_queue.put({'type': 'status', 'message': f"Connecté: {node_info}"})
        message_queue.put({'type': 'interface_ready', 'interface': interface})
    except Exception as e:
        logging.error(f"Erreur dans on_connection: {e}", exc_info=True)
        message_queue.put({'type': 'error', 'message': f"Erreur on_connection: {e}"})

def meshtastic_thread_func(queue_ref, stop_event):
    """Fonction exécutée dans le thread Meshtastic."""
    global meshtastic_interface
    meshtastic_interface = None

    pub.subscribe(on_receive, "meshtastic.receive")
    pub.subscribe(on_connection, "meshtastic.connection.established")
    pub.subscribe(lambda message: queue_ref.put({'type': 'status', 'message': f"Déconnecté: {message}"}), "meshtastic.connection.lost")
    pub.subscribe(lambda message: queue_ref.put({'type': 'error', 'message': f"Erreur Meshtastic: {message}"}), "meshtastic.connection.failed")

    while not stop_event.is_set():
        interface = None
        devPath = None
        try:
            queue_ref.put({'type': 'status', 'message': "Scan des ports..."})
            try:
                ports = meshtastic.util.findPorts(True)
                logging.debug(f"Ports détectés par findPorts: {ports}")
                if not ports:
                    logging.warning("meshtastic.util.findPorts n'a trouvé aucun port série.")
                    devPath = None
                elif len(ports) > 1:
                    logging.warning(f"Plusieurs ports Meshtastic détectés: {ports}. Utilisation du premier: {ports[0]}")
                    devPath = ports[0]
                else:
                    devPath = ports[0]
                    logging.info(f"Port detecté: {devPath}")

            except Exception as e_find:
                 logging.error(f"Erreur lors de la recherche explicite de port avec findPorts: {e_find}", exc_info=True)
                 queue_ref.put({'type': 'error', 'message': f"Erreur recherche port: {e_find}. Tentative sans port spécifié."})
                 devPath = None

            if devPath:
                queue_ref.put({'type': 'status', 'message': f"Tentative de connexion sur {devPath}..."})
                logging.info(f"Tentative de connexion à Meshtastic sur le port: {devPath}")
            else:
                 queue_ref.put({'type': 'status', 'message': "Connexion en cours..."})

            interface = meshtastic.serial_interface.SerialInterface(devPath=devPath, noProto=False)
            logging.info(f"Connexion série établie avec succès sur {interface.devPath if interface else 'N/A'}")

            while not stop_event.is_set():
                if interface is None or not hasattr(interface, 'stream') or not interface.stream.is_open:
                    logging.warning("Interface ou flux série semble perdu/fermé, tentative de reconnexion.")
                    meshtastic_interface = None
                    is_meshtastic_running = False
                    queue_ref.put({'type': 'status', 'message': "Connexion perdue, reconnexion..."})
                    break
                time.sleep(0.5)

        except serial.SerialException as e_serial:
            error_msg = f"Erreur Pyserial: {e_serial}"
            logging.error(error_msg, exc_info=True)
            queue_ref.put({'type': 'error', 'message': f"{error_msg}. Vérifiez le port/connexion. Réessai dans 5s."})
            stop_event.wait(5)
        except meshtastic.MeshtasticError as e_mesh:
            error_msg = f"Erreur Meshtastic: {e_mesh}"
            logging.error(error_msg, exc_info=True)
            queue_ref.put({'type': 'error', 'message': f"{error_msg}. Réessai dans 5s."})
            stop_event.wait(5)
        except Exception as e_generic:
            error_msg = f"Erreur inattendue: {e_generic}"
            logging.error(error_msg, exc_info=True)
            queue_ref.put({'type': 'error', 'message': f"{error_msg}. Réessai dans 5s."})
            stop_event.wait(5)
        finally:
            logging.debug("Nettoyage dans finally du thread Meshtastic...")
            if interface:
                try:
                    logging.debug(f"Fermeture de l'interface {type(interface)}...")
                    interface.close()
                    logging.debug("Interface fermée.")
                except Exception as e_close:
                    logging.error(f"Erreur lors de la fermeture de l'interface: {e_close}", exc_info=True)

            if meshtastic_interface is not None:
                 logging.debug("Réinitialisation de meshtastic_interface global à None.")
                 meshtastic_interface = None
                 is_meshtastic_running = False
                 if not stop_event.is_set():
                      queue_ref.put({'type': 'status', 'message': "Déconnecté"})

            if stop_event.is_set():
                logging.info("Arrêt demandé, sortie du thread Meshtastic.")
                break
            logging.debug("Pause avant la prochaine tentative de connexion...")
            stop_event.wait(1)

    logging.info("Thread Meshtastic terminé.")
    queue_ref.put({'type': 'status', 'message': "Thread Meshtastic arrêté."})
    meshtastic_interface = None
    is_meshtastic_running = False

# --- Fonctions de l'Application GUI (tournent dans le thread principal) ---

def initialize_udp_socket():
    """Initialise le socket UDP pour la communication avec Unity."""
    global udp_socket
    try:
        udp_socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        logging.info(f"Socket UDP initialisé pour envoi vers {UNITY_IP}:{UNITY_PORT}")
        return True
    except Exception as e:
        logging.error(f"Erreur lors de l'initialisation du socket UDP: {e}")
        return False

def get_node_short_name(node_id_hex):
    """Récupère le nom court d'un nœud à partir de son ID hexadécimal."""
    global meshtastic_interface
    if meshtastic_interface and hasattr(meshtastic_interface, 'nodes'):
        try:
            for node in meshtastic_interface.nodes.values():
                if hasattr(node, 'user') and hasattr(node.user, 'id') and node.user.id.endswith(node_id_hex):
                    if hasattr(node.user, 'shortName'):
                        return node.user.shortName
        except Exception as e:
            logging.warning(f"Erreur lors de la récupération du nom court pour {node_id_hex}: {e}")
    return None

def process_meshtastic_packet(packet):
    """Traite un paquet Meshtastic reçu via la queue."""
    global udp_socket

    if udp_socket is None:
        if not initialize_udp_socket():
            app.update_log("Échec de l'initialisation du socket UDP.", level='error')
            return

    sender_id_hex = packet.get('fromId', 'inconnu')
    short_name = get_node_short_name(sender_id_hex)
    display_name = f"{short_name} ({sender_id_hex})" if short_name else sender_id_hex

    # Traitement des messages texte (canned messages)
    if packet.get('decoded') and (packet['decoded'].get('portnum') == 1 or packet['decoded'].get('portnum') == "TEXT_MESSAGE_APP"):
        text = None
        if 'text' in packet['decoded']:
            text = packet['decoded']['text']
        elif 'payload' in packet['decoded']:
            try:
                text = packet['decoded']['payload']
                if isinstance(text, bytes):
                    text = text.decode('utf-8', errors='ignore')
            except Exception:
                text = str(packet['decoded']['payload'])
        
        app.update_log(f"Message: {display_name} - {text}", level='warning')

        data_for_unity = {
            "type": "text",
            "nodeId": sender_id_hex,
            "message": text
        }
        if udp_socket:
            try:
                message_json = json.dumps(data_for_unity)
                message_bytes = message_json.encode('utf-8')
                udp_socket.sendto(message_bytes, (UNITY_IP, UNITY_PORT))
            except Exception as e_udp:
                app.update_log(f"UDP: Erreur d'envoi du message - {e_udp}", level='error')
        return

    # Traitement des paquets de position
    if packet.get('decoded') and (packet['decoded'].get('portnum') == 3 or packet['decoded'].get('portnum') == "POSITION_APP"):
        latitude = None
        longitude = None
        altitude = 0
        position_data = None

        if 'position' in packet.get('decoded', {}):
            position_data = packet['decoded']['position']
        elif 'raw' in packet.get('decoded', {}) and 'position' in packet['decoded'].get('raw', {}):
            position_data = packet['decoded']['raw']['position']
        elif 'position' in packet:
            position_data = packet['position']

        if position_data:
            if 'latitudeI' in position_data:
                latitude = position_data.get('latitudeI') / 1e7
                longitude = position_data.get('longitudeI') / 1e7
            elif 'latitude_i' in position_data:
                latitude = position_data.get('latitude_i') / 1e7
                longitude = position_data.get('longitude_i') / 1e7
            elif 'latitude' in position_data:
                latitude = position_data.get('latitude')
                longitude = position_data.get('longitude')
            
            altitude = position_data.get('altitude', 0)

        if latitude is not None and longitude is not None:
            if latitude == 0 and longitude == 0: # Check for default/invalid GPS
                logging.warning(f"Coordonnées invalides (lat=0, lon=0) pour {display_name}, paquet ignoré.")
                return

            # Extraire la vitesse du paquet de position
            speed = 0.0
            if position_data:
                speed = position_data.get('groundSpeed', position_data.get('speed', 0.0))
            
            speed_knots = speed * 1.944
            app.update_log(f"GPS: {display_name} | Lat={latitude:.6f} Lon={longitude:.6f} Vitesse={speed:.1f}m/s ({speed_knots:.1f} nœuds)", level='warning')

            data_for_unity = {
                "type": "gps",
                "nodeId": sender_id_hex,
                "latitude": latitude,
                "longitude": longitude,
                "speed": speed,  # Ajouter la vitesse
                "heading": position_data.get('groundTrack', 0.0) if position_data else 0.0,  # Ajouter le cap
                "altitude": altitude,
                "timestamp": time.time()
            }

            if udp_socket:
                try:
                    message_json = json.dumps(data_for_unity)
                    message_bytes = message_json.encode('utf-8')
                    udp_socket.sendto(message_bytes, (UNITY_IP, UNITY_PORT))
                except Exception as e_udp:
                    app.update_log(f"UDP: Erreur d'envoi - {e_udp}", level='error')
                    if reset_udp_socket():
                        try:
                            udp_socket.sendto(message_bytes, (UNITY_IP, UNITY_PORT))
                        except Exception as e_retry:
                            app.update_log(f"UDP: Erreur après réinitialisation - {e_retry}", level='error')
            return
        else:
            logging.warning(f"GPS: Coordonnées non trouvées dans le paquet pour {display_name}, paquet ignoré.")
            return
    
    logging.debug(f"Paquet non traité: Type={packet.get('decoded', {}).get('portnum', 'N/A')}")

def reset_udp_socket(app_instance=None):
    """Force la réinitialisation du socket UDP."""
    global udp_socket, app
    log_app = app_instance if app_instance is not None else app

    if log_app:
        log_app.update_log("Réinitialisation forcée du socket UDP...", level='warning')
    else:
        logging.warning("Réinitialisation forcée du socket UDP...")

    if udp_socket is not None:
        try:
            udp_socket.close()
            if log_app:
                log_app.update_log("Socket UDP fermé.")
            else:
                logging.info("Socket UDP fermé.")
        except Exception as e:
            if log_app:
                log_app.update_log(f"Erreur lors de la fermeture du socket UDP: {e}", level='warning')
            else:
                logging.warning(f"Erreur lors de la fermeture du socket UDP: {e}")
        finally:
            udp_socket = None

    if initialize_udp_socket():
        if log_app:
            log_app.update_log("Socket UDP réinitialisé avec succès.", level='info')
        else:
            logging.info("Socket UDP réinitialisé avec succès.")
        send_test_udp_packet(log_app)
        return True
    else:
        if log_app:
            log_app.update_log("Échec de la réinitialisation du socket UDP.", level='error')
        else:
            logging.error("Échec de la réinitialisation du socket UDP.")
        return False

def send_test_udp_packet(app_instance=None):
    """Envoie un paquet UDP de test pour vérifier que le socket fonctionne."""
    global udp_socket, app
    log_app = app_instance if app_instance is not None else app

    if udp_socket is None:
        if log_app:
            log_app.update_log("Impossible d'envoyer un paquet de test: Socket UDP non initialisé", level='error')
        else:
            logging.error("Impossible d'envoyer un paquet de test: Socket UDP non initialisé")
        return False

    try:
        test_data = {
            "nodeId": "TEST",
            "latitude": 48.8566,
            "longitude": 2.3522,
            "altitude": 0,
            "test": True
        }
        message_json = json.dumps(test_data)
        message_bytes = message_json.encode('utf-8')
        logging.debug(f"Envoi UDP TEST: {message_json} vers {UNITY_IP}:{UNITY_PORT}")
        udp_socket.sendto(message_bytes, (UNITY_IP, UNITY_PORT))

        if log_app:
            log_app.update_log(f"UDP TEST: Paquet envoyé → {UNITY_IP}:{UNITY_PORT}")
        else:
            logging.info(f"UDP TEST: Paquet envoyé → {UNITY_IP}:{UNITY_PORT}")
        return True
    except Exception as e:
        if log_app:
            log_app.update_log(f"UDP TEST: Erreur lors de l'envoi du paquet de test - {e}", level='error')
        else:
            logging.error(f"UDP TEST: Erreur lors de l'envoi du paquet de test - {e}")
        logging.error(f"Erreur lors de l'envoi UDP de test: {e}", exc_info=True)
        return False

def check_udp_socket(app_instance=None):
    """Vérifie périodiquement l'état du socket UDP et le réinitialise si nécessaire."""
    global udp_socket, app
    log_app = app_instance if app_instance is not None else app

    if log_app is None:
        logging.debug("check_udp_socket: app n'est pas encore défini, vérification reportée")
        return

    if udp_socket is None:
        log_app.update_log("Socket UDP non initialisé lors de la vérification périodique", level='warning')
        if initialize_udp_socket():
            log_app.update_log("Socket UDP réinitialisé avec succès lors de la vérification périodique", level='info')
        else:
            log_app.update_log("Échec de la réinitialisation du socket UDP lors de la vérification périodique", level='error')
    else:
        try:
            if udp_socket.fileno() == -1:
                log_app.update_log("Socket UDP fermé ou invalide, tentative de réinitialisation", level='warning')
                if initialize_udp_socket():
                    log_app.update_log("Socket UDP réinitialisé avec succès", level='info')
                else:
                    log_app.update_log("Échec de la réinitialisation du socket UDP", level='error')
        except Exception as e:
            log_app.update_log(f"Erreur lors de la vérification du socket UDP: {e}", level='warning')
            if initialize_udp_socket():
                log_app.update_log("Socket UDP réinitialisé avec succès après erreur", level='info')
            else:
                log_app.update_log("Échec de la réinitialisation du socket UDP après erreur", level='error')

    log_app.root.after(UDP_CHECK_INTERVAL, lambda: check_udp_socket(log_app))

def send_to_unity(data):
    """Envoie des données à Unity via UDP."""
    global udp_socket
    try:
        if udp_socket is None:
            udp_socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        
        json_data = json.dumps(data)
        udp_socket.sendto(json_data.encode('utf-8'), (UNITY_IP, UNITY_PORT))
        
    except Exception as e:
        logging.error(f"Erreur envoi UDP: {e}")

# --- Classe Principale de l'Application GUI ---

class MeshtasticApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Meshtastic UDP Relay")

        try:
            if 'sv_ttk' in globals():
                sv_ttk.set_theme("light")
        except Exception as e:
            logging.warning(f"Impossible d'appliquer le thème Sun Valley: {e}")

        self.main_frame = ttk.Frame(root, padding="10")
        self.main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        root.columnconfigure(0, weight=1)
        root.rowconfigure(0, weight=1)

        # Section Connexion
        conn_frame = ttk.LabelFrame(self.main_frame, text="Connexion Meshtastic", padding="10")
        conn_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), padx=5, pady=5)
        conn_frame.columnconfigure(1, weight=1)

        self.status_label = ttk.Label(conn_frame, text="État: Déconnecté", width=40)
        self.status_label.grid(row=0, column=0, padx=5, pady=5, sticky=tk.W)

        self.start_button = ttk.Button(conn_frame, text="Démarrer", command=self.start_meshtastic)
        self.start_button.grid(row=0, column=1, padx=5, pady=5, sticky=tk.E)

        self.stop_button = ttk.Button(conn_frame, text="Arrêter", command=self.stop_meshtastic, state=tk.DISABLED)
        self.stop_button.grid(row=0, column=2, padx=5, pady=5, sticky=tk.E)

        # Section Logs
        log_frame = ttk.LabelFrame(self.main_frame, text="Logs et Paquets Reçus", padding="10")
        log_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), padx=5, pady=5)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        self.main_frame.rowconfigure(1, weight=1)

        self.log_area = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD, height=15, width=80)
        self.log_area.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        self.log_area.configure(state='disabled')

        # Section Contrôle UDP
        udp_control_frame = ttk.LabelFrame(self.main_frame, text="Contrôle UDP", padding="10")
        udp_control_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), padx=5, pady=5)
        udp_control_frame.columnconfigure(0, weight=1)
        udp_control_frame.columnconfigure(1, weight=1)

        self.reset_udp_button = ttk.Button(udp_control_frame, text="Réinitialiser UDP",
                                         command=lambda: reset_udp_socket(self))
        self.reset_udp_button.grid(row=0, column=0, padx=5, pady=5, sticky=tk.W+tk.E)

        self.test_udp_button = ttk.Button(udp_control_frame, text="Envoyer Test UDP",
                                        command=lambda: send_test_udp_packet(self))
        self.test_udp_button.grid(row=0, column=1, padx=5, pady=5, sticky=tk.W+tk.E)


        # Initialiser le socket UDP
        if not initialize_udp_socket():
            self.update_log("ERREUR: Échec de l'init UDP. Le relais ne fonctionnera pas.", level='error')
        else:
            send_test_udp_packet(self)

        # Lancer la vérification périodique de la queue
        self.check_queue()

        # Lancer la vérification périodique de l'état de l'interface
        self.check_interface_state()

        # Lancer la vérification périodique du socket UDP
        check_udp_socket(self)

        # Gérer la fermeture de la fenêtre
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)


    def update_log(self, message, level='info'):
        """Ajoute un message au widget de log."""
        timestamp = time.strftime("%H:%M:%S")
        prefix = f"[{timestamp}][{level.upper()}] "
        full_message = prefix + str(message) + "\n"

        self.log_area.configure(state='normal')
        self.log_area.insert(tk.END, full_message)
        self.log_area.see(tk.END)
        self.log_area.configure(state='disabled')

        if level == 'error':
            logging.error(message)
        elif level == 'warning':
            logging.warning(message)
        elif level == 'debug':
            logging.debug(message)
        else:
            logging.info(message)


    def check_interface_state(self):
        """Vérifie périodiquement l'état de l'interface Meshtastic et met à jour l'UI."""
        global meshtastic_interface, is_meshtastic_running

        # Just update status, no config buttons to enable/disable based on Meshtastic connection for this simplified version
        # The start/stop buttons are handled directly in check_queue.

        if is_meshtastic_running and meshtastic_interface is not None:
            try:
                if hasattr(meshtastic_interface, 'myInfo'):
                    pass
            except Exception as e:
                self.update_log(f"Interface Meshtastic invalide: {e}", level='warning')
                meshtastic_interface = None

        self.root.after(1000, self.check_interface_state)

    def check_queue(self):
        """Vérifie la queue pour les messages du thread Meshtastic."""
        global is_meshtastic_running

        try:
            while True:
                message_item = message_queue.get_nowait()

                msg_type = message_item.get('type')
                if msg_type == 'status':
                    self.status_label.config(text=f"État: {message_item.get('message', 'N/A')}")
                    self.update_log(message_item.get('message', 'Message de statut inconnu'))
                    if "Connecté" in message_item.get('message', ''):
                        is_meshtastic_running = True
                        self.start_button.config(state=tk.DISABLED)
                        self.stop_button.config(state=tk.NORMAL)
                    elif "arrêté" in message_item.get('message', '') or "Déconnecté" in message_item.get('message', ''):
                        is_meshtastic_running = False
                        self.start_button.config(state=tk.NORMAL)
                        self.stop_button.config(state=tk.DISABLED)

                elif msg_type == 'error':
                    self.update_log(message_item.get('message', 'Erreur inconnue'), level='error')
                    self.status_label.config(text=f"État: Erreur - voir logs")
                    self.stop_button.config(state=tk.NORMAL)

                elif msg_type == 'meshtastic_packet':
                    process_meshtastic_packet(message_item.get('data'))

                elif msg_type == 'interface_ready':
                    self.update_log("Interface Meshtastic prête", level='info')
                    is_meshtastic_running = True
                    global udp_socket
                    if udp_socket is None:
                        self.update_log("Réinitialisation du socket UDP...", level='info')
                        if initialize_udp_socket():
                            self.update_log("Socket UDP réinitialisé avec succès", level='info')
                        else:
                            self.update_log("Échec de la réinitialisation du socket UDP", level='error')

                else:
                    self.update_log(f"Type de message inconnu reçu: {msg_type}", level='warning')

        except queue.Empty:
            pass
        except Exception as e:
            self.update_log(f"Erreur dans check_queue: {e}", level='error')

        self.root.after(100, self.check_queue)

    def start_meshtastic(self):
        """Démarre le thread Meshtastic."""
        global meshtastic_thread, stop_thread_event, is_meshtastic_running
        if meshtastic_thread is not None and meshtastic_thread.is_alive():
            self.update_log("Meshtastic est déjà en cours.", level='warning')
            return

        self.update_log("Démarrage du thread Meshtastic...")
        stop_thread_event.clear()
        meshtastic_thread = threading.Thread(target=meshtastic_thread_func,
                                             args=(message_queue, stop_thread_event),
                                             daemon=True)
        meshtastic_thread.start()

        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        is_meshtastic_running = True
        self.status_label.config(text="État: Connexion en cours...")

    def stop_meshtastic(self):
        """Arrête le thread Meshtastic."""
        global meshtastic_thread, stop_thread_event, is_meshtastic_running, meshtastic_interface

        if hasattr(self, 'stop_clicked') and self.stop_clicked:
            self.update_log("Arrêt forcé de la connexion Meshtastic...", level='warning')
            self._force_stop_meshtastic()
            return

        self.stop_clicked = True
        self.update_log("Arrêt de la connexion Meshtastic...", level='warning')

        stop_thread_event.set()

        if meshtastic_interface:
            try:
                meshtastic_interface.close()
                self.update_log("Interface Meshtastic fermée", level='info')
            except Exception as e:
                self.update_log(f"Erreur lors de la fermeture de l'interface: {e}", level='error')
            finally:
                meshtastic_interface = None

        if meshtastic_thread is not None:
            try:
                meshtastic_thread.join(timeout=2.0)
                if meshtastic_thread.is_alive():
                    self.update_log("Le thread Meshtastic n'a pas pu être arrêté proprement.", level='warning')
                    self.update_log("Cliquez à nouveau sur 'Arrêter' pour forcer l'arrêt.", level='warning')
                    self.stop_button.config(state=tk.NORMAL)
                    return
            except Exception as e:
                self.update_log(f"Erreur lors de l'attente de la fin du thread: {e}", level='error')
            finally:
                if not meshtastic_thread.is_alive():
                    meshtastic_thread = None

        self._reset_ui_state()
        message_queue.put({'type': 'status', 'message': "Déconnecté manuellement"})
        self.update_log("Connexion Meshtastic arrêtée", level='warning')

    def _force_stop_meshtastic(self):
        """Force l'arrêt du thread Meshtastic (méthode agressive)."""
        global meshtastic_thread, meshtastic_interface, is_meshtastic_running

        if meshtastic_interface:
            try:
                meshtastic_interface.close()
            except:
                pass
            meshtastic_interface = None

        meshtastic_thread = None
        self._reset_ui_state()
        self.update_log("Connexion Meshtastic arrêtée de force", level='warning')

    def _reset_ui_state(self):
        """Réinitialise l'état de l'interface utilisateur après déconnexion."""
        global is_meshtastic_running

        self.stop_clicked = False
        is_meshtastic_running = False

        self.status_label.config(text="État: Déconnecté")
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)

    def on_closing(self):
        """Gère la fermeture de la fenêtre."""
        if messagebox.askokcancel("Quitter", "Voulez-vous vraiment quitter ?"):
            try:
                self.stop_meshtastic()
                if udp_socket:
                    udp_socket.close()
                    logging.info("Socket UDP fermé")
            except Exception as e:
                logging.error(f"Erreur lors de la fermeture: {e}")
            finally:
                self.root.destroy()

# --- Point d'Entrée ---
if __name__ == "__main__":
    logging.basicConfig(level=LOG_LEVEL, format='%(asctime)s - %(levelname)s - %(threadName)s - %(message)s')
    root = tk.Tk()
    app = MeshtasticApp(root)
    root.mainloop()
